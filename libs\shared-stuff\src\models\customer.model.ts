import { <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { ApiHideProperty } from '@nestjs/swagger';
import { IsEmail, IsString, Min } from 'class-validator';
import * as moment from 'moment-timezone';
import { Document, Types } from 'mongoose';
import { EnumField } from '../decorators/enum-field';
import { Field } from '../decorators/field';
import { IsPhoneNumberForRegion } from '../decorators/is-phone-number-by-region';
import { EmbeddedBranchDto } from '../dtos/brand/embedded-branch.dto';
import { EmbeddedBrandDto } from '../dtos/brand/embedded-brand.dto';
import { CustomerEarnedBenefit } from '../dtos/customer/customer-earned-benefit.dto';
import { CustomerTier } from '../dtos/customer/customer-tier.dto';
import { CustomerUsedBenefit } from '../dtos/customer/customer-used-benefit.dto';
import { DeviceData } from '../dtos/customer/device-data.dto';
import { EarnedReward } from '../dtos/customer/earned-reward.dto';
import { EmbeddedTierDto } from '../dtos/customer/embedded-tier.dto';
import { GooglePassLinks } from '../dtos/customer/google-pass-links.dto';
import { PunchCardProgress } from '../dtos/customer/punch-card-progress.dto';
import { RegisteredPass } from '../dtos/customer/registered-pass.dto';
import { RegistrationContextDto } from '../dtos/customer/registration-context.dto';
import { UsedReward } from '../dtos/customer/used-reward.dto';
import { VerificationContextDto } from '../dtos/customer/verification-context.dto';
import { EmbeddedOrderDto } from '../dtos/order/embedded-order.dto';
import { CustomerOrdableStores } from '../dtos/webstore/customer-ordable-stores.dto';
import { ContactChannel } from '../enums/customer/contact-channel.enum';
import { CustomerTitle } from '../enums/customer/customer-title.enum';
import { Gender } from '../enums/customer/gender.enum';
import { LoyaltyCardStatus } from '../enums/customer/loyalty-card-status.enum';
import { LoyaltyStatus } from '../enums/customer/loyalty-status.enum';
import { TierStatus } from '../enums/customer/tier-status.enum';
import { WalletApp } from '../enums/customer/wallet-app.enum';
import { CollectionName } from '../enums/general/collection-name.enum';
import { Language } from '../enums/general/language.enum';
import { Month } from '../enums/general/month.enum';
import { OrderSource } from '../enums/order/order-source.enum';
import { OrdableEntityInterface } from '../interfaces/webstore/ordable-entity.interface';
import { CountryDialCode } from '../types/general/country-code.type';
import { CurrentUser } from '../types/general/current-user';

export type CustomerDocument = Customer & Document;
@Schema({ timestamps: true })
export class Customer implements OrdableEntityInterface {
  @Field({
    type: String,
    modelOptions: {
      lowercase: true,
      required: false,
    },
  })
  full_name?: string;

  @Field({
    type: String,
    additionalDecorators: [IsString()],
  })
  first_name: string;

  @Field({
    type: String,
    required: false,
    default: '',
  })
  last_name: string;

  @Field({
    type: String,
    required: true,
    additionalDecorators: [IsPhoneNumberForRegion('country_code')],
  })
  phone: string;

  @EnumField({
    type: String,
    required: true,
    enum: CountryDialCode,
    default: CountryDialCode.QATAR,
  })
  country_code: CountryDialCode;

  @Field({
    type: String,
    required: false,
    additionalDecorators: [IsEmail()],
  })
  email?: string;

  @Field({
    type: Number,
    default: 0,
  })
  total_orders_amount: number;

  @Field({
    type: Number,
    default: 0,
  })
  number_of_orders: number;

  @Field({
    type: Number,
    default: 0,
  })
  average_order_value: number;

  @Field({
    type: Date,
    required: false,
  })
  last_order_date?: Date;

  @Field({
    type: Date,
    required: false,
  })
  firstOrderDate?: Date;

  @EnumField({
    type: String,
    required: false,
    enum: Gender,
    default: Gender.male,
  })
  gender? = Gender.male;

  @EnumField({
    type: String,
    required: false,
    enum: Language,
    default: Language.english,
  })
  language? = Language.english;

  @Field({
    type: Types.ObjectId,
    required: false,
    modelOptions: {
      ref: CollectionName.ORDER,
    },
  })
  last_order_id?: Types.ObjectId;

  @Field({
    type: Types.ObjectId,
    required: false,
    modelOptions: {
      ref: CollectionName.PAYMENT,
    },
  })
  latestPayment?: Types.ObjectId;

  @Field({
    type: Date,
    required: false,
  })
  latestPaymentDate?: Date;

  @Field({
    type: String,
    required: false,
  })
  dibsyId?: string;

  @EnumField({
    type: String,
    enum: ContactChannel,
    default: ContactChannel.UNKNOWN,
    required: false,
  })
  contact_channel: ContactChannel | OrderSource = ContactChannel.UNKNOWN;

  @EnumField({
    type: String,
    enum: OrderSource,
    default: OrderSource.UNKNOWN,
    required: false,
  })
  latestContactChannel: OrderSource = OrderSource.UNKNOWN;

  @Field({
    type: Types.ObjectId,
    isArray: true,
    required: false,
    modelOptions: {
      ref: CollectionName.SAVED_LOCATION,
    },
    default: [],
  })
  savedLocations?: Types.ObjectId[] = [];

  @Field({
    type: EmbeddedOrderDto,
    required: false,
  })
  orders?: EmbeddedOrderDto[];

  @Field({
    type: Types.ObjectId,
    modelOptions: {
      ref: CollectionName.COMPANY,
    },
  })
  company: Types.ObjectId;

  @Field({
    type: CurrentUser,
    required: false,
  })
  assignedTo?: CurrentUser;

  @Field({ type: () => EmbeddedBranchDto, required: false })
  firstBranchOrderd?: EmbeddedBranchDto;

  @Field({ type: () => EmbeddedBrandDto, required: false })
  firstBrandOrderd?: EmbeddedBrandDto;

  @Field({ type: () => EmbeddedBrandDto, required: false })
  activeBrand?: EmbeddedBrandDto;

  @Field({
    type: Number,
    default: 0,
    additionalDecorators: [Min(0)],
  })
  loyaltyPoints = 0;

  @Field({
    type: EmbeddedTierDto,
    required: false,
  })
  loyaltyTier?: EmbeddedTierDto;

  @Field({ type: Number, required: false })
  carryOverPointsRate?: number;

  @Field({
    type: Number,
    required: false,
  })
  carryOverOrderRate?: number;

  @Field({
    type: Number,
    required: false,
  })
  carryOverAmountSpent?: number;

  @Field({
    type: Date,
    required: false,
  })
  firstTierEarnedAt?: Date;

  @Field({
    type: Date,
    required: false,
  })
  tierUpdatedAt?: Date;

  @Field({
    type: Date,
    required: false,
  })
  carryOverUpdatedAt?: Date;

  @Field({
    type: Date,
    required: false,
  })
  loyaltyRegistrationAt?: Date;

  @Field({
    type: Types.ObjectId,
    required: false,
  })
  loyaltyRegistrationBranchId?: Types.ObjectId;

  @Field({
    type: RegisteredPass,
    isArray: true,
    required: false,
  })
  registeredPasses: RegisteredPass[];

  @Field({ type: String, isArray: true, required: false })
  activeWalletPassMessages?: string[];

  @EnumField({
    type: String,
    enum: CustomerTitle,
    required: false,
  })
  title?: CustomerTitle;

  @Field({
    type: Date,
    required: false,
    description: "ISO format, e.g. '1999-10-28'",
  })
  birthDate?: Date;

  @EnumField({
    type: String,
    enum: LoyaltyStatus,
    required: true,
    default: LoyaltyStatus.UNENROLLED,
  })
  loyaltyStatus: LoyaltyStatus;

  @EnumField({
    type: String,
    enum: LoyaltyCardStatus,
    required: true,
    default: LoyaltyCardStatus.NOT_ADDED,
  })
  loyaltyCardStatus: LoyaltyCardStatus | WalletApp;

  @Field({ type: CustomerTier, required: false })
  tier?: CustomerTier;

  @EnumField({
    type: String,
    enum: TierStatus,
    required: true,
    default: TierStatus.NO_TIER,
  })
  tierStatus: TierStatus;

  @Field({
    type: DeviceData,
    required: false,
  })
  deviceData?: DeviceData;

  @Field({
    type: VerificationContextDto,
    required: false,
    additionalDecorators: [ApiHideProperty()],
  })
  verificationContext?: VerificationContextDto;

  @Field({
    type: RegistrationContextDto,
    required: false,
    additionalDecorators: [ApiHideProperty()],
  })
  registrationContext?: RegistrationContextDto;

  @Field({
    type: CustomerOrdableStores,
    required: false,
  })
  ordableStores?: CustomerOrdableStores;

  @Field({
    type: String,
    required: false,
    description:
      'Unique code that may be based on customer name and phone number. Used as pass QR code and customer lookup.',
  })
  shortCode?: string;

  @Field({
    type: String,
    required: false,
  })
  loopyCustomerId?: string;

  @Field({
    type: String,
    required: false,
  })
  brandWalletCustomerId?: string;

  @Field({
    type: String,
    required: false,
  })
  brandWalletPassBarcode?: string;

  @Field({
    type: Boolean,
    required: false,
    default: false,
  })
  isMigrated?: boolean;

  @Field({
    type: String,
    isArray: true,
    required: false,
    description: 'Codes used previously.',
  })
  codeHistory?: string[];

  @Field({
    type: PunchCardProgress,
    isArray: true,
    required: false,
  })
  punchCardProgress?: PunchCardProgress[];

  @Field({
    type: EarnedReward,
    isArray: true,
    required: false,
  })
  rewards?: EarnedReward[];

  @Field({
    type: UsedReward,
    isArray: true,
    required: false,
  })
  usedRewards?: UsedReward[];

  @Field({
    type: CustomerEarnedBenefit,
    isArray: true,
    required: false,
  })
  earnedBenefits?: CustomerEarnedBenefit[];

  @Field({
    type: CustomerUsedBenefit,
    isArray: true,
    required: false,
  })
  usedBenefits?: CustomerUsedBenefit[];

  @Field({
    type: GooglePassLinks,
    required: false,
  })
  googlePassLinks?: GooglePassLinks;

  @Field({
    type: Number,
    excludeFromDocs: true,
    default: () => moment().week(),
  })
  week: number;

  @Field({
    type: Number,
    excludeFromDocs: true,
    default: () => moment().date(),
  })
  day: number;

  @EnumField({
    type: String,
    enum: Month,
    excludeFromDocs: true,
    default: () => moment().startOf('month').format('MMMM'),
  })
  month: string;

  @Field({
    type: Number,
    excludeFromDocs: true,
    default: () => moment().year(),
  })
  year: number;

  @Field({
    type: CurrentUser,
    required: false,
    excludeFromDocs: true,
  })
  createdBy?: CurrentUser;

  @Field({
    type: Date,
    required: false,
    excludeFromDocs: true,
  })
  createdAt?: Date;

  @Field({
    type: CurrentUser,
    required: false,
    excludeFromDocs: true,
  })
  updatedBy?: CurrentUser;

  @Field({
    type: Date,
    required: false,
    excludeFromDocs: true,
  })
  updatedAt?: Date;

  @Field({
    type: CurrentUser,
    required: false,
    excludeFromDocs: true,
  })
  deletedBy?: CurrentUser;

  @Field({
    type: Date,
    required: false,
    excludeFromDocs: true,
  })
  deletedAt?: Date;
}
const CustomerSchema = SchemaFactory.createForClass(Customer);

CustomerSchema.index({ full_name: 1 });
CustomerSchema.index({ number_of_orders: 1 });
CustomerSchema.index({ total_orders_amount: 1 });
CustomerSchema.index({ createdAt: -1 });
CustomerSchema.index({ updatedAt: 1 });
CustomerSchema.index({ company: 1, phone: 1, deletedAt: 1 }, { unique: true });
CustomerSchema.index(
  { company: 1, shortCode: 1, deletedAt: 1 },
  { unique: true },
);

export { CustomerSchema };
